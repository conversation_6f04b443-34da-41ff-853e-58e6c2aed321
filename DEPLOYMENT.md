# 部署指南：Netlify + Vercel 全栈架构

本项目采用前后端分离架构：
- **前端**：部署到 Netlify（静态网站托管）
- **后端**：部署到 Vercel（Node.js 服务）

## 🚀 快速部署

### 1. 后端部署到 Vercel

#### 步骤 1：准备 Vercel 账户
1. 访问 [vercel.com](https://vercel.com) 并注册/登录
2. 连接你的 GitHub 账户

#### 步骤 2：部署后端
1. 在 Vercel 控制台点击 "New Project"
2. 选择你的 GitHub 仓库
3. 配置项目设置：
   - **Framework Preset**: Other
   - **Root Directory**: `backend`
   - **Build Command**: 留空
   - **Output Directory**: 留空
   - **Install Command**: `npm install`

#### 步骤 3：配置环境变量
在 Vercel 项目设置中添加以下环境变量：

```bash
# 必需的环境变量
NODE_ENV=production
PORT=3000

# 邮件服务配置
SMTP_HOST=smtp.qq.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-email-auth-code

# 支付宝配置
ALIPAY_APP_ID=your-alipay-app-id
ALIPAY_PRIVATE_KEY_PATH=./keys/app-private-key.pem
ALIPAY_PUBLIC_KEY_PATH=./keys/alipay-public-key.pem

# 前端域名（用于 CORS）
FRONTEND_URL=https://your-site-name.netlify.app
```

#### 步骤 4：上传支付宝密钥文件
1. 在 `backend/keys/` 目录下放置支付宝密钥文件
2. 确保文件名与环境变量中的路径匹配

### 2. 前端部署到 Netlify

#### 步骤 1：准备 Netlify 账户
1. 访问 [netlify.com](https://netlify.com) 并注册/登录
2. 连接你的 GitHub 账户

#### 步骤 2：部署前端
1. 在 Netlify 控制台点击 "New site from Git"
2. 选择你的 GitHub 仓库
3. 配置构建设置：
   - **Base directory**: `frontend`
   - **Build command**: 留空
   - **Publish directory**: `frontend`
4. 点击 "Deploy site"

#### 步骤 3：更新配置
1. 记录 Netlify 分配的域名（如：`https://amazing-site-123456.netlify.app`）
2. 记录 Vercel 分配的域名（如：`https://your-backend.vercel.app`）

### 3. 连接前后端

#### 步骤 1：更新前端配置
编辑 `frontend/config.js`：

```javascript
production: {
    API_BASE_URL: 'https://your-backend.vercel.app', // 替换为你的 Vercel 域名
    PAYMENT_ENABLED: true
},
```

#### 步骤 2：更新后端 CORS 配置
编辑 `backend/server.js` 中的 CORS 配置：

```javascript
const corsOptions = {
    origin: [
        'https://your-site-name.netlify.app', // 替换为你的 Netlify 域名
        'https://your-custom-domain.com'      // 如果有自定义域名
    ],
    // ... 其他配置
};
```

#### 步骤 3：重新部署
1. 提交代码更改到 GitHub
2. Netlify 和 Vercel 会自动重新部署

## 🔧 高级配置

### 自定义域名

#### Netlify 自定义域名
1. 在 Netlify 项目设置中点击 "Domain management"
2. 添加自定义域名
3. 按照指示配置 DNS 记录

#### Vercel 自定义域名
1. 在 Vercel 项目设置中点击 "Domains"
2. 添加自定义域名
3. 按照指示配置 DNS 记录

### 环境变量管理

#### 开发环境
复制 `backend/.env.example` 为 `backend/.env` 并填入实际值。

#### 生产环境
在 Vercel 控制台的 Environment Variables 中配置。

### 监控和日志

#### Vercel 日志
- 在 Vercel 控制台的 "Functions" 标签页查看日志
- 使用 `console.log()` 进行调试

#### Netlify 日志
- 在 Netlify 控制台的 "Deploys" 标签页查看构建日志

## 🐛 常见问题

### CORS 错误
确保后端的 CORS 配置包含了前端的域名。

### 支付功能不工作
1. 检查支付宝密钥文件是否正确上传
2. 验证环境变量配置
3. 查看 Vercel 函数日志

### 图片加载失败
确保图片文件在 `frontend/images/` 目录下，并且路径正确。

## 📞 技术支持

如果遇到部署问题，请检查：
1. GitHub 仓库是否公开或已正确授权
2. 环境变量是否正确配置
3. 域名配置是否正确
4. 查看部署日志获取详细错误信息
