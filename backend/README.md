# 艺境光影摄影工作室 - 后端服务

## 项目简介

这是艺境光影摄影工作室的后端服务，提供支付宝支付集成、订单管理和邮件通知功能。

## 安装依赖

```bash
cd backend
npm install
```

## 环境配置

创建 `.env` 文件并配置以下环境变量：

```env
# 服务器配置
PORT=3000
NODE_ENV=development
BASE_URL=http://localhost:3000

# 支付宝配置
ALIPAY_APP_ID=your_app_id
ALIPAY_PRIVATE_KEY_PATH=./keys/app-private-key.pem
ALIPAY_PUBLIC_KEY_PATH=./keys/alipay-public-key.pem

# 邮件配置
SMTP_HOST=smtp.qq.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_email_password
```

## 支付宝配置

1. 访问 [支付宝开放平台](https://open.alipay.com/)
2. 注册开发者账号并创建应用
3. 生成RSA密钥对：
   ```bash
   mkdir keys
   # 生成应用私钥
   openssl genrsa -out keys/app-private-key.pem 2048
   # 从私钥生成公钥
   openssl rsa -in keys/app-private-key.pem -pubout -out keys/app-public-key.pem
   ```
4. 将应用公钥上传到支付宝开放平台
5. 下载支付宝公钥保存为 `keys/alipay-public-key.pem`

## 启动服务

### 开发环境
```bash
npm run dev
```

### 生产环境
```bash
npm start
```

## API接口

### 创建支付订单
- **URL**: `POST /api/create-payment`
- **参数**:
  ```json
  {
    "photoId": "photo_id",
    "price": 29,
    "userEmail": "<EMAIL>",
    "photoTitle": "作品标题"
  }
  ```

### 查询支付状态
- **URL**: `GET /api/payment-status/:orderNo`

### 支付宝异步通知
- **URL**: `POST /api/alipay-notify`

### 健康检查
- **URL**: `GET /api/health`

## 注意事项

1. 生产环境请使用真实的数据库替换内存存储
2. 邮件服务需要正确配置SMTP信息
3. 支付宝沙箱环境和生产环境需要不同的配置
4. 确保服务器域名已备案（生产环境）
5. 使用HTTPS协议（生产环境）

## 目录结构

```
backend/
├── server.js          # 主服务文件
├── package.json       # 依赖配置
├── .env.example       # 环境变量示例
├── keys/              # 密钥文件目录
│   ├── app-private-key.pem
│   ├── app-public-key.pem
│   └── alipay-public-key.pem
└── README.md          # 说明文档
```