const express = require('express');
const cors = require('cors');
const path = require('path');
const AlipaySdk = require('alipay-sdk').default;
const fs = require('fs');
const nodemailer = require('nodemailer');
require('dotenv').config();

// 环境变量检查函数
function checkRequiredEnvVars() {
    const requiredVars = [
        'SMTP_HOST',
        'SMTP_PORT',
        'SMTP_USER',
        'SMTP_PASS',
        'ALIPAY_APP_ID'
    ];

    const missingVars = [];

    for (const varName of requiredVars) {
        if (!process.env[varName]) {
            missingVars.push(varName);
        }
    }

    // 检查支付宝密钥配置
    const hasPrivateKey = process.env.ALIPAY_PRIVATE_KEY_PATH && fs.existsSync(process.env.ALIPAY_PRIVATE_KEY_PATH);

    const hasPublicKey = process.env.ALIPAY_PUBLIC_KEY_PATH && fs.existsSync(process.env.ALIPAY_PUBLIC_KEY_PATH);

    if (!hasPrivateKey) {
        missingVars.push('ALIPAY_PRIVATE_KEY_PATH - 密钥文件');
    }

    if (!hasPublicKey) {
        missingVars.push('ALIPAY_PUBLIC_KEY_PATH - 公钥文件');
    }

    if (missingVars.length > 0) {
        const errorMessage = `缺少必需的环境变量配置：\n${missingVars.map(v => `  - ${v}`).join('\n')}\n\n请在 .env 文件中配置这些环境变量，或者设置对应的系统环境变量。`;
        console.error('\n❌ 环境变量配置错误:');
        console.error(errorMessage);
        throw new Error(errorMessage);
    }

    console.log('✅ 所有必需的环境变量已正确配置');
}

// 检查环境变量
try {
    checkRequiredEnvVars();
} catch (error) {
    console.error('服务器启动失败:', error.message);
    process.exit(1);
}

// 创建Express应用
const app = express();
const PORT = process.env.PORT;

// 中间件配置
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(express.static(path.join(__dirname, '../frontend')));

// 简单的内存数据库（生产环境应使用真实数据库）
let orders = new Map();

// 邮件配置
const transporter = nodemailer.createTransport({
    host: process.env.SMTP_HOST,
    port: process.env.SMTP_PORT,
    secure: false,
    auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS
    }
});

// 初始化支付宝SDK
const alipaySdk = new AlipaySdk({
    appId: process.env.ALIPAY_APP_ID,
    privateKey: fs.readFileSync(process.env.ALIPAY_PRIVATE_KEY_PATH, 'ascii'),
    alipayPublicKey: fs.readFileSync(process.env.ALIPAY_PUBLIC_KEY_PATH, 'ascii'),
    gateway: process.env.NODE_ENV === 'production' ? 'https://openapi.alipay.com/gateway.do' : 'https://openapi.alipaydev.com/gateway.do',
});

// 数据库操作函数
async function saveOrder(orderData) {
    orders.set(orderData.orderNo, {
        ...orderData,
        createdAt: new Date(),
        updatedAt: new Date()
    });
    console.log('订单已保存:', orderData.orderNo);
    return orderData;
}

async function updateOrderStatus(orderNo, status) {
    const order = orders.get(orderNo);
    if (order) {
        order.status = status;
        order.updatedAt = new Date();
        orders.set(orderNo, order);
        console.log('订单状态已更新:', orderNo, status);
    }
    return order;
}

async function getOrderByNo(orderNo) {
    return orders.get(orderNo);
}

// 邮件发送函数
async function sendPhotoToEmail(email, photoData) {
    try {
        const mailOptions = {
            from: process.env.SMTP_USER,
            to: email,
            subject: `您购买的摄影作品 - ${photoData.photoTitle || '高清原图'}`,
            html: `
                <h2>感谢您的购买！</h2>
                <p>您购买的摄影作品《${photoData.photoTitle || '高清原图'}》已经为您准备好了。</p>
                <p>订单号：${photoData.orderNo}</p>
                <p>由于技术限制，请联系我们的客服获取高清原图文件。</p>
                <p>客服邮箱：<EMAIL></p>
                <p>客服电话：86-190-4276-2541</p>
                <p>感谢您对艺境光影的支持！</p>
            `
        };
        
        await transporter.sendMail(mailOptions);
        console.log('邮件发送成功:', email);
    } catch (error) {
        console.error('邮件发送失败:', error);
        // 邮件发送失败不应该影响支付流程
    }
}

// 输入验证中间件
function validatePaymentRequest(req, res, next) {
    const { photoId, price, userEmail, photoTitle } = req.body;
    
    if (!photoId || !price || !userEmail || !photoTitle) {
        return res.status(400).json({
            success: false,
            message: '缺少必要参数'
        });
    }
    
    if (price <= 0 || price > 1000) {
        return res.status(400).json({
            success: false,
            message: '价格无效'
        });
    }
    
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(userEmail)) {
        return res.status(400).json({
            success: false,
            message: '邮箱格式无效'
        });
    }
    
    next();
}

// 创建支付订单
app.post('/api/create-payment', validatePaymentRequest, async (req, res) => {
    const { photoId, price, userEmail, photoTitle } = req.body;
    
    // 生成唯一订单号
    const outTradeNo = `PHOTO_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    try {
        // 调用支付宝预下单接口
        const result = await alipaySdk.exec('alipay.trade.precreate', {
            bizContent: {
                out_trade_no: outTradeNo,
                total_amount: price.toString(),
                subject: `摄影作品：${photoTitle}`,
                body: `购买高清摄影作品 - ${photoTitle}`,
                timeout_express: '10m', // 10分钟支付超时
                notify_url: `${process.env.BASE_URL || 'http://localhost:3000'}/api/alipay-notify`, // 异步通知地址
            }
        });
        
        console.log('支付宝预下单结果:', result);
        
        if (result.code === '10000') {
            // 保存订单信息到数据库
            await saveOrder({
                orderNo: outTradeNo,
                photoId,
                price,
                userEmail,
                photoTitle,
                qrCode: result.qr_code, // 支付宝返回的字段是qr_code
                status: 'pending'
            });
            
            res.json({
                success: true,
                orderNo: outTradeNo,
                qrCode: result.qr_code // 支付宝返回的字段是qr_code，不是qrCode
            });
        } else {
            res.json({
                success: false,
                message: result.msg || result.sub_msg || '创建支付订单失败'
            });
        }
    } catch (error) {
        console.error('支付宝下单失败:', error);
        res.status(500).json({
            success: false,
            message: '创建支付订单失败'
        });
    }
});

// 支付宝异步通知处理
app.post('/api/alipay-notify', async (req, res) => {
    try {
        console.log('收到支付宝通知:', req.body);
        
        // 验证签名
        const isValid = alipaySdk.checkNotifySign(req.body);
        
        if (isValid) {
            const { out_trade_no, trade_status, total_amount } = req.body;
            
            if (trade_status === 'TRADE_SUCCESS') {
                // 支付成功，更新订单状态
                await updateOrderStatus(out_trade_no, 'paid');
                
                // 发送邮件等后续处理
                const order = await getOrderByNo(out_trade_no);
                if (order) {
                    await sendPhotoToEmail(order.userEmail, order);
                }
            }
            
            res.send('success'); // 返回success告知支付宝处理成功
        } else {
            console.error('支付宝通知签名验证失败');
            res.send('fail');
        }
    } catch (error) {
        console.error('处理支付宝通知失败:', error);
        res.send('fail');
    }
});

// 查询支付状态
app.get('/api/payment-status/:orderNo', async (req, res) => {
    const { orderNo } = req.params;
    
    try {
        const result = await alipaySdk.exec('alipay.trade.query', {
            bizContent: {
                out_trade_no: orderNo
            }
        });
        
        console.log('查询支付状态结果:', result);
        
        res.json({
            success: true,
            status: result.trade_status, // 支付宝返回的字段是trade_status
            orderNo: orderNo
        });
    } catch (error) {
        console.error('查询支付状态失败:', error);
        res.status(500).json({
            success: false,
            message: '查询支付状态失败'
        });
    }
});

// 自动读取图片文件夹并生成作品数据
app.get('/api/photography-data', async (req, res) => {
    try {
        const imagesPath = path.join(__dirname, '../frontend/images');
        const photographyData = [];

        // 分类映射
        const categoryMap = {
            'Animal': 'animal',
            'Architectural': 'architecture',
            'Food': 'food',
            'Landscape': 'landscape',
            'Portrait': 'portrait',
            'Realistic': 'realistic'
        };

        // 分类中文名称
        const categoryNames = {
            'animal': '动物',
            'architecture': '建筑',
            'food': '食物',
            'landscape': '风景',
            'portrait': '人物',
            'realistic': '写实'
        };

        // 读取所有分类文件夹
        const categories = fs.readdirSync(imagesPath, { withFileTypes: true })
            .filter(dirent => dirent.isDirectory())
            .map(dirent => dirent.name);

        let idCounter = 1;

        for (const categoryFolder of categories) {
            const categoryKey = categoryMap[categoryFolder];
            if (!categoryKey) continue; // 跳过未映射的文件夹

            const categoryPath = path.join(imagesPath, categoryFolder);

            try {
                const files = fs.readdirSync(categoryPath)
                    .filter(file => /\.(jpg|jpeg|png|gif|webp)$/i.test(file));

                for (const file of files) {
                    // 生成作品数据
                    const photoData = {
                        id: idCounter++,
                        title: generateTitle(categoryKey, file),
                        description: generateDescription(categoryKey),
                        category: categoryKey,
                        price: generatePrice(categoryKey),
                        isPaid: Math.random() > 0.4, // 60% 概率为付费作品
                        image: `images/${categoryFolder}/${file}`
                    };

                    // 如果是免费作品，价格设为0
                    if (!photoData.isPaid) {
                        photoData.price = 0;
                    }

                    photographyData.push(photoData);
                }
            } catch (error) {
                console.warn(`读取分类文件夹失败: ${categoryFolder}`, error.message);
            }
        }

        res.json({
            success: true,
            data: photographyData,
            count: photographyData.length
        });

    } catch (error) {
        console.error('读取摄影作品数据失败:', error);
        res.status(500).json({
            success: false,
            message: '读取摄影作品数据失败'
        });
    }
});

// 生成作品标题
function generateTitle(category, filename) {
    const titles = {
        landscape: ['晨曦山峦', '湖光倒影', '云海奇观', '夕阳西下', '雪山之巅', '森林秘境'],
        portrait: ['优雅肖像', '街头瞬间', '光影人像', '情感写真', '时尚大片', '自然表情'],
        food: ['美食艺术', '甜品时光', '精致料理', '诱人美味', '色香味俱全', '烹饪艺术'],
        animal: ['野生精灵', '宠物写真', '自然生灵', '动物世界', '生命力量', '可爱瞬间'],
        architecture: ['现代建筑', '复古建筑', '钢筋森林', '建筑美学', '城市印象', '设计之美'],
        realistic: ['城市印象', '生活瞬间', '街角故事', '真实记录', '日常之美', '人文关怀']
    };

    const categoryTitles = titles[category] || ['摄影作品'];
    const randomTitle = categoryTitles[Math.floor(Math.random() * categoryTitles.length)];

    // 添加文件名的一部分作为唯一标识
    const fileId = filename.split('.')[0].slice(-4);
    return `${randomTitle} ${fileId}`;
}

// 生成作品描述
function generateDescription(category) {
    const descriptions = {
        landscape: [
            '清晨第一缕阳光洒向群山，金辉万丈，美不胜收',
            '静谧湖水倒映着蓝天白云，宛如仙境',
            '云海翻腾，山峦若隐若现，如梦如幻',
            '夕阳西下，天空被染成金黄色，温暖而宁静'
        ],
        portrait: [
            '光影交织下的人物肖像，诠释独特气质',
            '捕捉城市中人们的真实瞬间',
            '自然光线下的完美表情，展现内心世界',
            '时尚与艺术的完美结合，彰显个性魅力'
        ],
        food: [
            '精致摆盘，诱人美食的视觉盛宴',
            '诱人甜品，生活中的小确幸',
            '色香味俱全的料理艺术，满足视觉与味觉',
            '烹饪的艺术，每一道菜都是一件艺术品'
        ],
        animal: [
            '自然中的野生动物，展现生命的力量',
            '记录毛孩子们的可爱瞬间',
            '动物世界的精彩瞬间，生命的美好',
            '人与动物和谐相处的温馨画面'
        ],
        architecture: [
            '现代建筑的独特设计，展现城市的未来感',
            '复古建筑的独特设计，展现城市的历史感',
            '摩天大楼林立，现代都市的钢铁诗篇',
            '建筑美学与功能的完美结合'
        ],
        realistic: [
            '繁华都市中的真实面貌，展现城市生活的多样性',
            '记录日常生活中的真实片段，平凡却动人的时光',
            '街头巷尾的人情冷暖，每个角落都有故事',
            '真实记录生活的美好瞬间，发现平凡中的不平凡'
        ]
    };

    const categoryDescriptions = descriptions[category] || ['精美的摄影作品，值得收藏'];
    return categoryDescriptions[Math.floor(Math.random() * categoryDescriptions.length)];
}

// 生成作品价格
function generatePrice(category) {
    const priceRanges = {
        landscape: [25, 29, 35, 39],
        portrait: [35, 39, 45, 49],
        food: [20, 25, 29, 35],
        animal: [30, 35, 39, 45],
        architecture: [25, 29, 35, 39],
        realistic: [20, 25, 29, 35]
    };

    const prices = priceRanges[category] || [25, 29, 35];
    return prices[Math.floor(Math.random() * prices.length)];
}

// 健康检查接口
app.get('/api/health', (req, res) => {
    res.json({
        status: 'ok',
        message: '服务运行正常',
        timestamp: new Date().toISOString()
    });
});

// 首页路由
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, '../frontend/index.html'));
});

// 错误处理中间件
app.use((err, req, res, next) => {
    console.error('服务器错误:', err);
    res.status(500).json({
        success: false,
        message: '服务器内部错误'
    });
});

// 启动服务器
app.listen(PORT, () => {
    console.log(`服务器运行在端口 ${PORT}`);
    console.log(`环境: ${process.env.NODE_ENV || 'development'}`);
    console.log(`支付宝网关: ${alipaySdk.config.gateway}`);
});

module.exports = app;