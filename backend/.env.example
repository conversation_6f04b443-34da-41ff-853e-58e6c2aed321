# 服务器配置
PORT=3000
NODE_ENV=development
BASE_URL=http://localhost:3000

# 支付宝配置
# 应用ID - 从支付宝开放平台获取
ALIPAY_APP_ID=your_alipay_app_id
ALIPAY_PRIVATE_KEY_PATH=./keys/app-private-key.pem
ALIPAY_PUBLIC_KEY_PATH=./keys/alipay-public-key.pem

# 邮件服务配置
# SMTP服务器地址
SMTP_HOST=smtp.qq.com
# SMTP端口
SMTP_PORT=587
# 发送邮件的邮箱地址
SMTP_USER=<EMAIL>
# 邮箱授权码（不是登录密码）
SMTP_PASS=your_email_auth_code

# 其他邮箱服务商配置示例：
# 
# 163邮箱：
# SMTP_HOST=smtp.163.com
# SMTP_PORT=465
# SMTP_USER=<EMAIL>
# SMTP_PASS=your_auth_code
#
# Gmail：
# SMTP_HOST=smtp.gmail.com
# SMTP_PORT=587
# SMTP_USER=<EMAIL>
# SMTP_PASS=your_app_password

# 配置说明：
# 1. QQ邮箱需要开启SMTP服务并生成授权码（不是登录密码）
# 2. 163邮箱需要开启SMTP服务，端口使用465，启用SSL
# 3. Gmail需要开启两步验证并生成应用专用密码
# 4. 生产环境请使用HTTPS并配置正确的BASE_URL
# 5. 支付宝密钥文件需要放在keys目录下
# 6. 支付宝沙箱环境和生产环境会自动根据NODE_ENV切换
