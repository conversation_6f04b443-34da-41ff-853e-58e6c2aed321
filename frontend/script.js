// 摄影作品数据 - 将从后端API动态加载
let photographyData = [];

// 全局变量
let currentFilter = 'all';
let currentPhotoData = null;
let paymentPollingInterval = null;

// DOM 元素
const galleryGrid = document.getElementById('gallery-grid');
const tabButtons = document.querySelectorAll('.tab-button');
const paymentModal = document.getElementById('payment-modal');
const successModal = document.getElementById('success-modal');
const userEmailInput = document.getElementById('user-email');
const confirmedEmailSpan = document.getElementById('confirmed-email');
const photoPriceSpan = document.getElementById('photo-price');

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeNavigation();
    loadPhotographyData();
    initializeModalEvents();
});

// 从后端API加载摄影作品数据
async function loadPhotographyData() {
    try {
        // 显示加载状态
        showLoadingState();

        const apiUrl = window.APP_CONFIG?.API_BASE_URL
            ? `${window.APP_CONFIG.API_BASE_URL}/api/photography-data`
            : '/api/photography-data';
        const response = await fetch(apiUrl);
        const result = await response.json();

        if (result.success) {
            photographyData = result.data;
            console.log(`成功加载 ${result.count} 张摄影作品`);
            renderGallery();
        } else {
            console.error('加载摄影作品数据失败:', result.message);
            showErrorState('加载作品数据失败，请刷新页面重试');
        }
    } catch (error) {
        console.error('网络错误:', error);
        showErrorState('网络连接失败，请检查网络后刷新页面');
    }
}

// 显示加载状态
function showLoadingState() {
    const galleryGrid = document.getElementById('gallery-grid');
    galleryGrid.innerHTML = `
        <div class="loading-container" style="grid-column: 1 / -1; text-align: center; padding: 60px 20px;">
            <div class="loading-spinner" style="
                width: 40px;
                height: 40px;
                border: 4px solid #f3f3f3;
                border-top: 4px solid #007bff;
                border-radius: 50%;
                animation: spin 1s linear infinite;
                margin: 0 auto 20px;
            "></div>
            <p style="color: #666; font-size: 16px;">正在加载摄影作品...</p>
        </div>
        <style>
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
        </style>
    `;
}

// 显示错误状态
function showErrorState(message) {
    const galleryGrid = document.getElementById('gallery-grid');
    galleryGrid.innerHTML = `
        <div class="error-container" style="grid-column: 1 / -1; text-align: center; padding: 60px 20px;">
            <div style="font-size: 48px; color: #dc3545; margin-bottom: 20px;">⚠️</div>
            <h3 style="color: #dc3545; margin-bottom: 10px;">加载失败</h3>
            <p style="color: #666; margin-bottom: 20px;">${message}</p>
            <button onclick="loadPhotographyData()" style="
                background: #007bff;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                cursor: pointer;
                font-size: 14px;
            ">重新加载</button>
        </div>
    `;
}

// 导航栏功能
function initializeNavigation() {
    const hamburger = document.querySelector('.hamburger');
    const navMenu = document.querySelector('.nav-menu');
    
    if (hamburger && navMenu) {
        hamburger.addEventListener('click', function() {
            navMenu.classList.toggle('active');
        });
    }

    // 平滑滚动
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
                // 关闭移动端菜单
                if (navMenu) {
                    navMenu.classList.remove('active');
                }
            }
        });
    });
}

// 分类标签切换
tabButtons.forEach(button => {
    button.addEventListener('click', function() {
        // 移除所有活动状态
        tabButtons.forEach(btn => btn.classList.remove('active'));
        // 添加当前按钮的活动状态
        this.classList.add('active');
        
        // 获取分类
        currentFilter = this.getAttribute('data-category');
        
        // 重新渲染画廊
        renderGallery();
    });
});

// 渲染作品画廊
function renderGallery() {
    // 检查数据是否已加载
    if (!photographyData || photographyData.length === 0) {
        return;
    }

    const filteredData = currentFilter === 'all'
        ? photographyData
        : photographyData.filter(item => item.category === currentFilter);

    galleryGrid.innerHTML = '';

    // 如果没有符合条件的作品
    if (filteredData.length === 0) {
        galleryGrid.innerHTML = `
            <div class="no-data-container" style="grid-column: 1 / -1; text-align: center; padding: 60px 20px;">
                <div style="font-size: 48px; color: #ccc; margin-bottom: 20px;">📷</div>
                <h3 style="color: #666; margin-bottom: 10px;">暂无作品</h3>
                <p style="color: #999;">该分类下暂时没有摄影作品</p>
            </div>
        `;
        return;
    }

    filteredData.forEach(item => {
        const galleryItem = createGalleryItem(item);
        galleryGrid.appendChild(galleryItem);
    });

    // 添加加载动画
    galleryGrid.querySelectorAll('.gallery-item').forEach((item, index) => {
        item.style.animationDelay = `${index * 0.1}s`;
        item.classList.add('loading');
        setTimeout(() => {
            item.classList.add('loaded');
        }, 100 + index * 100);
    });
}

// 创建作品项目元素
function createGalleryItem(data) {
    const div = document.createElement('div');
    div.className = 'gallery-item';
    div.setAttribute('data-category', data.category);
    
    const categoryNames = {
        landscape: '风景',
        portrait: '人物',
        food: '食物',
        animal: '动物',
        architecture: '建筑',
        realistic: '写实'
    };
    
    div.innerHTML = `
        <img src="${data.image}" alt="${data.title}" data-loaded="false">
        <div class="gallery-item-content">
            <h3>${data.title}</h3>
            <p>${data.description}</p>
            <div class="gallery-item-footer">
                <span class="category-badge">${categoryNames[data.category]}</span>
                <span class="${data.isPaid ? 'price-tag' : 'free-tag'}">
                    ${data.isPaid ? `¥${data.price}` : '免费'}
                </span>
            </div>
        </div>
    `;
    
    // 图片加载事件
    const img = div.querySelector('img');
    img.onload = function() {
        this.setAttribute('data-loaded', 'true');
    };
    
    // 点击事件
    div.addEventListener('click', function() {
        if (data.isPaid) {
            openPaymentModal(data);
        } else {
            // 免费作品直接显示成功消息
            showFreeDownloadMessage(data);
        }
    });
    
    return div;
}

// 打开付费弹窗
function openPaymentModal(data) {
    currentPhotoData = data;
    photoPriceSpan.textContent = data.price;
    userEmailInput.value = '';
    paymentModal.style.display = 'block';
    document.body.style.overflow = 'hidden';
    
    // 重置二维码为默认状态
    const qrImage = document.getElementById('qr-image');
    qrImage.src = 'data:image/svg+xml;base64,' + btoa(`
        <svg width="200" height="200" xmlns="http://www.w3.org/2000/svg">
            <rect width="200" height="200" fill="#f0f0f0" stroke="#ccc" stroke-width="2"/>
            <text x="100" y="90" text-anchor="middle" font-family="Arial" font-size="14" fill="#666">请先输入邮箱地址</text>
            <text x="100" y="110" text-anchor="middle" font-family="Arial" font-size="12" fill="#999">然后点击"我已支付"生成二维码</text>
        </svg>
    `);
}

// 生成支付宝二维码
async function generatePaymentQR(data) {
    const qrImage = document.getElementById('qr-image');
    const userEmail = document.getElementById('user-email').value.trim();
    
    if (!userEmail || !isValidEmail(userEmail)) {
        alert('请先输入有效的邮箱地址');
        return;
    }
    
    try {
        // 显示加载状态
        qrImage.src = 'data:image/svg+xml;base64,' + btoa(`
            <svg width="200" height="200" xmlns="http://www.w3.org/2000/svg">
                <rect width="200" height="200" fill="#f0f0f0"/>
                <text x="100" y="100" text-anchor="middle" font-family="Arial" font-size="14" fill="#666">生成中...</text>
            </svg>
        `);
        
        // 调用后端API创建支付订单
        const apiUrl = window.APP_CONFIG?.API_BASE_URL
            ? `${window.APP_CONFIG.API_BASE_URL}/api/create-payment`
            : '/api/create-payment';
        const response = await fetch(apiUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                photoId: data.id || Date.now(),
                price: data.price,
                userEmail: userEmail,
                photoTitle: data.title
            })
        });
        
        const result = await response.json();
        
        if (result.success) {
            // 检查二维码数据格式
            if (result.qrCode) {
                // 如果是URL格式，生成二维码图片
                if (result.qrCode.startsWith('http')) {
                    // 使用第三方二维码生成服务
                    qrImage.src = `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(result.qrCode)}`;
                } else {
                    // 如果是base64格式，直接使用
                    qrImage.src = result.qrCode.startsWith('data:') ? result.qrCode : `data:image/png;base64,${result.qrCode}`;
                }
                
                // 保存订单号，用于查询支付状态
                currentPhotoData.orderNo = result.orderNo;
                
                // 开始轮询支付状态
                startPaymentStatusPolling(result.orderNo);
            } else {
                alert('支付二维码生成失败，请重试');
            }
        } else {
            alert('创建支付订单失败：' + result.message);
        }
    } catch (error) {
        console.error('生成支付二维码失败:', error);
        alert('网络错误，请重试');
    }
}

// 轮询支付状态
function startPaymentStatusPolling(orderNo) {
    // 清除之前的轮询
    if (paymentPollingInterval) {
        clearInterval(paymentPollingInterval);
    }
    
    paymentPollingInterval = setInterval(async () => {
        try {
            const apiUrl = window.APP_CONFIG?.API_BASE_URL
                ? `${window.APP_CONFIG.API_BASE_URL}/api/payment-status/${orderNo}`
                : `/api/payment-status/${orderNo}`;
            const response = await fetch(apiUrl);
            const result = await response.json();
            
            if (result.success) {
                if (result.status === 'TRADE_SUCCESS') {
                    // 支付成功
                    clearInterval(paymentPollingInterval);
                    paymentPollingInterval = null;
                    const userEmail = document.getElementById('user-email').value;
                    paymentSuccess(userEmail);
                } else if (result.status === 'TRADE_CLOSED' || result.status === 'TRADE_FINISHED') {
                    // 支付关闭或完成
                    clearInterval(paymentPollingInterval);
                    paymentPollingInterval = null;
                    if (result.status === 'TRADE_FINISHED') {
                        const userEmail = document.getElementById('user-email').value;
                        paymentSuccess(userEmail);
                    } else {
                        alert('支付已关闭');
                        closePaymentModal();
                    }
                }
            }
        } catch (error) {
            console.error('查询支付状态失败:', error);
        }
    }, 3000); // 每3秒查询一次
    
    // 10分钟后停止轮询
    setTimeout(() => {
        if (paymentPollingInterval) {
            clearInterval(paymentPollingInterval);
            paymentPollingInterval = null;
        }
    }, 600000);
}

// 修改确认支付函数
function confirmPayment() {
    const email = userEmailInput.value.trim();
    const confirmBtn = document.querySelector('.btn-primary');
    
    // 防止重复点击
    if (confirmBtn.disabled) {
        return;
    }
    
    if (!email) {
        alert('请输入邮箱地址');
        userEmailInput.focus();
        return;
    }
    
    if (!isValidEmail(email)) {
        alert('请输入有效的邮箱地址');
        userEmailInput.focus();
        return;
    }
    
    // 禁用按钮防止重复点击
    confirmBtn.disabled = true;
    const originalText = confirmBtn.textContent;
    confirmBtn.textContent = '生成中...';
    
    // 生成支付二维码
    generatePaymentQR(currentPhotoData).finally(() => {
        // 恢复按钮状态
        confirmBtn.disabled = false;
        confirmBtn.textContent = originalText;
    });
}

// 关闭付费弹窗
function closePaymentModal() {
    paymentModal.style.display = 'none';
    document.body.style.overflow = '';
    currentPhotoData = null;
    userEmailInput.value = '';
    
    // 清理支付状态轮询
    if (paymentPollingInterval) {
        clearInterval(paymentPollingInterval);
        paymentPollingInterval = null;
    }
}



// 显示支付处理中
function showPaymentProcessing() {
    const confirmBtn = document.querySelector('.btn-primary');
    const originalText = confirmBtn.textContent;
    confirmBtn.textContent = '验证支付中...';
    confirmBtn.disabled = true;
    
    setTimeout(() => {
        confirmBtn.textContent = originalText;
        confirmBtn.disabled = false;
    }, 2000);
}

// 支付成功
function paymentSuccess(email) {
    closePaymentModal();
    confirmedEmailSpan.textContent = email;
    successModal.style.display = 'block';
    document.body.style.overflow = 'hidden';
    
    // 模拟发送邮件
    sendPhotoToEmail(email, currentPhotoData);
}

// 模拟发送邮件
function sendPhotoToEmail(email, photoData) {
    console.log(`发送高清作品到邮箱: ${email}`);
    console.log(`作品信息:`, photoData);
}

// 关闭成功弹窗
function closeSuccessModal() {
    successModal.style.display = 'none';
    document.body.style.overflow = '';
}

// 显示免费下载消息
function showFreeDownloadMessage(data) {
    alert(`感谢您欣赏《${data.title}》！\\n\\n这是一张免费作品，您可以直接保存图片。\\n\\n如需更高分辨率版本，请联系我们的客服。`);
}

// 邮箱验证
function isValidEmail(email) {
    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;
    return emailRegex.test(email);
}

// 初始化弹窗事件
function initializeModalEvents() {
    // 关闭按钮事件
    document.querySelectorAll('.close').forEach(closeBtn => {
        closeBtn.addEventListener('click', function() {
            const modal = this.closest('.modal');
            modal.style.display = 'none';
            document.body.style.overflow = '';
        });
    });
    
    // 点击模态框外部关闭
    window.addEventListener('click', function(event) {
        if (event.target.classList.contains('modal')) {
            event.target.style.display = 'none';
            document.body.style.overflow = '';
        }
    });
    
    // ESC 键关闭弹窗
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape') {
            const visibleModal = document.querySelector('.modal[style*="block"]');
            if (visibleModal) {
                visibleModal.style.display = 'none';
                document.body.style.overflow = '';
            }
        }
    });
}

// 滚动效果
window.addEventListener('scroll', function() {
    const navbar = document.querySelector('.navbar');
    if (window.scrollY > 50) {
        navbar.style.background = 'rgba(255, 255, 255, 0.98)';
        navbar.style.boxShadow = '0 2px 20px rgba(0, 0, 0, 0.15)';
    } else {
        navbar.style.background = 'rgba(255, 255, 255, 0.95)';
        navbar.style.boxShadow = '0 2px 20px rgba(0, 0, 0, 0.1)';
    }
});