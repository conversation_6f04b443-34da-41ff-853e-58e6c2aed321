# 艺境光影摄影工作室 - 摄影作品展示网站

一个专业的摄影作品展示网站，支持免费和付费作品展示，具有完整的支付流程和用户体验。

## 🌟 主要功能

### 📸 作品展示
- **多分类展示**：风景、人物、食物、动物、建筑、写实摄影
- **智能筛选**：点击分类标签快速筛选作品
- **响应式设计**：完美适配桌面端和移动端设备
- **优雅动画**：流畅的加载和切换动画效果

### 💰 付费系统
- **免费/付费标识**：清晰区分免费和付费作品
- **支付流程**：点击付费作品弹出支付窗口
- **邮箱验证**：要求用户输入有效邮箱地址
- **支付宝二维码**：显示支付宝收款二维码
- **自动发送**：支付成功后自动发送高清作品到邮箱

### 🏢 商务信息
- **商家介绍**：专业的工作室介绍
- **联系方式**：电话、邮箱、微信等多种联系方式
- **服务条款**：详细的商品说明、退款政策、争议解决等
- **版权声明**：明确的版权保护条款

### 🎨 用户体验
- **现代化设计**：简洁美观的界面设计
- **平滑滚动**：丝滑的页面滚动体验
- **加载优化**：图片懒加载和性能优化
- **交互反馈**：悬停效果和点击反馈

## 🛠 技术特性

### 前端技术
- **HTML5**：语义化标签，良好的SEO
- **CSS3**：现代化样式，Flexbox/Grid布局
- **JavaScript ES6+**：原生JavaScript，无依赖
- **响应式设计**：移动优先的设计理念

### 功能特性
- **模块化设计**：清晰的代码结构
- **事件驱动**：高效的事件处理机制
- **数据驱动**：动态渲染作品内容
- **状态管理**：完善的界面状态控制

## 📁 项目结构

```
photography-portfolio/
├── index.html          # 主页面
├── styles.css          # 样式文件
├── script.js           # JavaScript功能
└── README.md           # 项目说明
```

## 🚀 快速开始

### 本地运行
1. 克隆项目到本地
2. 使用任何HTTP服务器打开项目
3. 或直接在浏览器中打开 `index.html`

## 💡 使用说明

### 浏览作品
1. 打开网站首页
2. 点击导航栏的"作品展示"或首页的"浏览作品"按钮
3. 使用分类标签筛选不同类型的摄影作品
4. 点击作品查看详情

### 购买付费作品
1. 点击标有价格的付费作品
2. 在弹出窗口中输入您的邮箱地址
3. 使用支付宝扫描二维码完成支付
4. 点击"我已支付"确认
5. 高清作品将发送到您的邮箱

### 免费作品
- 点击标有"免费"的作品可直接查看
- 免费作品可直接保存使用
- 如需更高分辨率版本，请联系客服

## 🎯 定制指南

### 修改作品内容
在 `script.js` 文件中的 `photographyData` 数组中添加或修改作品信息：

```javascript
{
    id: 13,
    title: "作品标题",
    description: "作品描述",
    category: "landscape", // 分类：landscape, portrait, food, animal, architecture, realistic
    price: 29,             // 价格，0表示免费
    isPaid: true,          // 是否付费
    image: "图片URL"       // 图片地址
}
```

### 修改商家信息
在 `index.html` 文件中修改：
- 工作室名称和介绍
- 联系方式信息
- 服务条款内容

### 修改样式
在 `styles.css` 文件中自定义：
- 颜色主题
- 字体样式
- 布局参数
- 动画效果

## 🔧 功能扩展

### 集成真实支付
```javascript
// 在 generatePaymentQR 函数中集成支付宝API
function generatePaymentQR(data) {
    // 调用支付宝API生成真实二维码
    // 或集成其他支付服务商
}
```

### 邮件服务集成
```javascript
// 在 sendPhotoToEmail 函数中集成邮件服务
function sendPhotoToEmail(email, photoData) {
    // 使用 EmailJS 或后端API发送邮件
}
```

### 后端集成
- 添加用户管理系统
- 实现订单管理
- 集成云存储服务
- 添加数据分析功能

## 🚀 下一步建议
- 测试网站功能: 在浏览器中打开网站，测试所有功能
- 自定义内容: 替换示例图片为您的真实摄影作品
- 集成真实支付: 对接支付宝官方API
- 邮件服务: 集成EmailJS或后端邮件服务
- 部署上线: 选择合适的托管服务上线网站
这个网站已经具备了一个专业摄影工作室所需的所有基础功能，您可以立即开始使用或进一步定制！🎊

## 📱 浏览器兼容性

- ✅ Chrome (推荐)
- ✅ Firefox
- ✅ Safari
- ✅ Edge
- ✅ 移动端浏览器

## 📞 技术支持

如有任何技术问题或定制需求，请联系：

- 📧 邮箱：<EMAIL>
- 📱 电话：400-888-9999
- 💬 微信：ArtPhoto2024

## 📄 许可证

本项目为商业用途设计，请根据实际需求调整许可证条款。

---

**艺境光影摄影工作室** - 用镜头捕捉生活中的每一个美好瞬间 📸
