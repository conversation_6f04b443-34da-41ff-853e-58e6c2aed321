// 环境配置
const CONFIG = {
    // 开发环境
    development: {
        API_BASE_URL: 'http://localhost:3000',
        PAYMENT_ENABLED: true
    },
    
    // 生产环境 - Netlify + 外部后端
    production: {
        API_BASE_URL: 'https://your-backend-url.herokuapp.com', // 替换为你的后端URL
        PAYMENT_ENABLED: true
    },
    
    // 静态演示环境 - 仅 Netlify
    static: {
        API_BASE_URL: '',
        PAYMENT_ENABLED: false
    }
};

// 自动检测环境
function getEnvironment() {
    const hostname = window.location.hostname;
    
    if (hostname === 'localhost' || hostname === '127.0.0.1') {
        return 'development';
    } else if (hostname.includes('netlify.app') || hostname.includes('your-domain.com')) {
        // 如果有后端服务，返回 'production'
        // 如果是纯静态，返回 'static'
        return 'static'; // 默认静态模式
    }
    
    return 'static';
}

// 获取当前配置
const currentConfig = CONFIG[getEnvironment()];

// 导出配置
window.APP_CONFIG = currentConfig;
