[build]
  # 如果只部署前端，设置基础目录
  base = "frontend"
  
  # 发布目录（相对于 base）
  publish = "."
  
  # 构建命令（静态文件无需构建）
  command = ""

[build.environment]
  # Node.js 版本
  NODE_VERSION = "18"

# 重定向规则 - 用于单页应用
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

# 头部设置 - 优化性能和安全
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"

# 图片优化
[[headers]]
  for = "/images/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000"

# CSS 和 JS 缓存
[[headers]]
  for = "*.css"
  [headers.values]
    Cache-Control = "public, max-age=31536000"

[[headers]]
  for = "*.js"
  [headers.values]
    Cache-Control = "public, max-age=31536000"
